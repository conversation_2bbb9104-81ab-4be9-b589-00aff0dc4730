/**
 * Global Search Functionality
 *
 * This script handles the global search functionality in the top navigation bar.
 * It sends AJAX requests to the server and displays the results in a dropdown.
 */

document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('globalSearch');
    const searchResults = document.getElementById('globalSearchResults');

    if (!searchInput || !searchResults) return;

    // Debounce function to prevent excessive API calls
    function debounce(func, wait) {
        let timeout;
        return function(...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        };
    }

    // Function to perform the search
    const performSearch = debounce(function(query) {
        if (query.length < 2) {
            searchResults.classList.remove('show');
            return;
        }

        // Show loading state
        searchResults.innerHTML = `
            <div class="search-results-header">
                <span><i class="fas fa-search me-2"></i> Searching...</span>
            </div>
            <div class="search-no-results">
                <div class="spinner-border text-success" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Searching for "${query}"...</p>
            </div>
        `;
        searchResults.classList.add('show');

        // Fetch search results from the server
        fetch(`/choims/modules/api/global_search.php?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    searchResults.innerHTML = `
                        <div class="search-results-header">
                            <span><i class="fas fa-exclamation-circle me-2"></i> Error</span>
                        </div>
                        <div class="search-no-results">
                            <p>${data.error}</p>
                        </div>
                    `;
                    return;
                }

                if (data.total === 0) {
                    searchResults.innerHTML = `
                        <div class="search-results-header">
                            <span><i class="fas fa-search me-2"></i> Search Results</span>
                        </div>
                        <div class="search-no-results">
                            <i class="fas fa-search"></i>
                            <p>No results found for "${query}"</p>
                            <p class="text-muted small">Try a different search term or check your spelling</p>
                        </div>
                    `;
                    return;
                }

                // Build the results HTML
                let resultsHTML = `
                    <div class="search-results-header">
                        <span><i class="fas fa-search me-2"></i> Search Results</span>
                        <span class="search-result-badge">${data.total} results</span>
                    </div>
                `;

                // Assets section
                if (data.assets.length > 0) {
                    resultsHTML += `
                        <div class="search-results-section">
                            <div class="search-results-section-title">
                                <i class="fas fa-laptop me-2"></i> Assets (${data.assets.length})
                            </div>
                    `;

                    data.assets.forEach(asset => {
                        // Determine status badge class
                        let statusClass = 'badge-info';
                        if (asset.status === 'In use') statusClass = 'badge-success';
                        if (asset.status === 'Under Repair') statusClass = 'badge-warning';
                        if (asset.status === 'Defective') statusClass = 'badge-danger';

                        resultsHTML += `
                            <div class="search-result-item" onclick="window.location.href='${asset.url}'">
                                <div class="search-result-icon">
                                    <i class="fas fa-laptop"></i>
                                </div>
                                <div class="search-result-content">
                                    <div class="search-result-title">${asset.name}</div>
                                    <div class="search-result-subtitle">
                                        <span>${asset.sku}</span>
                                        <span class="search-result-badge">${asset.category}</span>
                                        <span class="search-result-badge ${statusClass}">${asset.status}</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    resultsHTML += `</div>`;
                }

                // Consumables section
                if (data.consumables.length > 0) {
                    resultsHTML += `
                        <div class="search-results-section">
                            <div class="search-results-section-title">
                                <i class="fas fa-box me-2"></i> Consumables (${data.consumables.length})
                            </div>
                    `;

                    data.consumables.forEach(item => {
                        // Determine status badge class
                        let statusClass = 'badge-info';
                        if (item.status === 'Out of Stock') statusClass = 'badge-danger';
                        else if (item.status === 'Low Stock') statusClass = 'badge-warning';
                        else statusClass = 'badge-success';

                        resultsHTML += `
                            <div class="search-result-item" onclick="window.location.href='${item.url}'">
                                <div class="search-result-icon">
                                    <i class="fas fa-box"></i>
                                </div>
                                <div class="search-result-content">
                                    <div class="search-result-title">${item.name}</div>
                                    <div class="search-result-subtitle">
                                        <span>${item.sku}</span>
                                        <span class="search-result-badge">${item.category}</span>
                                        <span class="search-result-badge ${statusClass}">${item.status} (${item.quantity})</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    resultsHTML += `</div>`;
                }

                // Locations section
                if (data.locations && data.locations.length > 0) {
                    resultsHTML += `
                        <div class="search-results-section">
                            <div class="search-results-section-title">
                                <i class="fas fa-map-marker-alt me-2"></i> Locations (${data.locations.length})
                            </div>
                    `;

                    data.locations.forEach(location => {
                        resultsHTML += `
                            <div class="search-result-item" onclick="window.location.href='${location.url}'">
                                <div class="search-result-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="search-result-content">
                                    <div class="search-result-title">${location.name}</div>
                                    <div class="search-result-subtitle">
                                        <span class="search-result-badge">${location.type}</span>
                                        <span>${location.address || 'No address'}</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    resultsHTML += `</div>`;
                }

                // Transactions section
                if (data.transactions && data.transactions.length > 0) {
                    resultsHTML += `
                        <div class="search-results-section">
                            <div class="search-results-section-title">
                                <i class="fas fa-exchange-alt me-2"></i> Transactions (${data.transactions.length})
                            </div>
                    `;

                    data.transactions.forEach(transaction => {
                        // Determine status badge class
                        let statusClass = 'badge-info';
                        if (transaction.status === 'Completed') statusClass = 'badge-success';
                        else if (transaction.status === 'Rejected') statusClass = 'badge-danger';
                        else if (transaction.status.includes('Approved')) statusClass = 'badge-warning';
                        else statusClass = 'badge-secondary';

                        // Format date
                        const date = new Date(transaction.date);
                        const formattedDate = date.toLocaleDateString();

                        resultsHTML += `
                            <div class="search-result-item" onclick="window.location.href='${transaction.url}'">
                                <div class="search-result-icon">
                                    <i class="fas fa-exchange-alt"></i>
                                </div>
                                <div class="search-result-content">
                                    <div class="search-result-title">${transaction.code}</div>
                                    <div class="search-result-subtitle">
                                        <span>${transaction.item}</span>
                                        <span class="search-result-badge ${statusClass}">${transaction.status}</span>
                                        <span class="search-result-date">${formattedDate}</span>
                                    </div>
                                    <div class="search-result-details">
                                        <small>${transaction.source} → ${transaction.destination}</small>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    resultsHTML += `</div>`;
                }

                // Batch Transfers section
                if (data.batch_transfers && data.batch_transfers.length > 0) {
                    resultsHTML += `
                        <div class="search-results-section">
                            <div class="search-results-section-title">
                                <i class="fas fa-boxes me-2"></i> Batch Transfers (${data.batch_transfers.length})
                            </div>
                    `;

                    data.batch_transfers.forEach(batch => {
                        // Determine status badge class
                        let statusClass = 'badge-info';
                        if (batch.status === 'Completed') statusClass = 'badge-success';
                        else if (batch.status === 'Rejected') statusClass = 'badge-danger';
                        else if (batch.status.includes('Approved')) statusClass = 'badge-warning';
                        else statusClass = 'badge-secondary';

                        // Format date
                        const date = new Date(batch.date);
                        const formattedDate = date.toLocaleDateString();

                        resultsHTML += `
                            <div class="search-result-item" onclick="window.location.href='${batch.url}'">
                                <div class="search-result-icon">
                                    <i class="fas fa-boxes"></i>
                                </div>
                                <div class="search-result-content">
                                    <div class="search-result-title">${batch.code} <span class="search-result-badge badge-primary">Batch</span></div>
                                    <div class="search-result-subtitle">
                                        <span>${batch.item_summary}</span>
                                        <span class="search-result-badge ${statusClass}">${batch.status}</span>
                                        <span class="search-result-date">${formattedDate}</span>
                                    </div>
                                    <div class="search-result-details">
                                        <small>${batch.source} → ${batch.destination}</small>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    resultsHTML += `</div>`;
                }

                // Add "View All" link if there are more results
                if (data.total > 15) {
                    resultsHTML += `
                        <div class="search-view-all" onclick="window.location.href='/choims/modules/search/index.php?q=${encodeURIComponent(query)}'">
                            <i class="fas fa-search me-2"></i> View all ${data.total} results
                        </div>
                    `;
                }

                searchResults.innerHTML = resultsHTML;
            })
            .catch(error => {
                console.error('Error performing search:', error);
                searchResults.innerHTML = `
                    <div class="search-results-header">
                        <span><i class="fas fa-exclamation-circle me-2"></i> Error</span>
                    </div>
                    <div class="search-no-results">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>An error occurred while searching</p>
                        <p class="text-muted small">Please try again later</p>
                    </div>
                `;
            });
    }, 300);

    // Event listener for input changes
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();
        performSearch(query);
    });

    // Event listener for focus
    searchInput.addEventListener('focus', function() {
        const query = this.value.trim();
        if (query.length >= 2) {
            performSearch(query);
        }
    });

    // Close search results when clicking outside
    document.addEventListener('click', function(event) {
        if (!searchInput.contains(event.target) && !searchResults.contains(event.target)) {
            searchResults.classList.remove('show');
        }
    });

    // Prevent clicks inside the search results from closing the dropdown
    searchResults.addEventListener('click', function(event) {
        event.stopPropagation();
    });

    // Handle keyboard navigation
    searchInput.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            searchResults.classList.remove('show');
            searchInput.blur();
        }
    });
});
